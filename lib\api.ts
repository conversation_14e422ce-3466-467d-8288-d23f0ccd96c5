import axios from 'axios';
import Cookies from 'js-cookie';

// Prevent multiple redirects on 401
let isRedirecting = false;

// Create axios instance
const api = axios.create({
  withCredentials: true,
});

// Add a request interceptor to automatically attach Authorization header
api.interceptors.request.use(
  config => {
    const token =
      typeof window !== 'undefined' ? Cookies.get('authToken') : null;

    // Add Authorization header if token exists and not already present
    if (token && !config.headers.Authorization) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      console.log('401 detected by interceptor');
      if (typeof window !== 'undefined' && !isRedirecting) {
        if (
          window.location.pathname !== '/auth/login' &&
          window.location.pathname !== '/auth/signup'
        ) {
          isRedirecting = true;

          // Clear all authentication data
          localStorage.removeItem('persist:root');
          localStorage.removeItem('authToken');
          Cookies.remove('roleName', { path: '/' });
          localStorage.removeItem('fullName');
          sessionStorage.clear();

          // Dispatch auth clear action if store is available
          try {
            (window as any).store?.dispatch({ type: 'auth/clearAuthState' });
          } catch (storeError) {
            console.warn('Failed to dispatch clearAuthState:', storeError);
          }

          // Redirect to login and reset flag after navigation
          console.log('Redirecting to login due to 401');
          window.location.href = '/auth/login';

          // Reset the flag after a short delay to prevent race conditions
          setTimeout(() => {
            isRedirecting = false;
          }, 1000);
        } else {
          // If already on login/signup page, just clear the data without redirect
          localStorage.removeItem('persist:root');
          localStorage.removeItem('authToken');
          Cookies.remove('roleName', { path: '/' });
          localStorage.removeItem('fullName');
          sessionStorage.clear();

          try {
            (window as any).store?.dispatch({ type: 'auth/clearAuthState' });
          } catch (storeError) {
            console.warn('Failed to dispatch clearAuthState:', storeError);
          }
        }
      }
    }
    return Promise.reject(error);
  }
);

export default api;
