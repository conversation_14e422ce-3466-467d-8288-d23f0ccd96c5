// config.ts
import { readFileSync } from 'fs';
import { load } from 'js-yaml';
import { plainToInstance, Type } from 'class-transformer';
import {
  validateSync,
  IsString,
  IsNumber,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { getConfigPath } from 'src/utils/helper.util';

// === Config Classes ===

class MongoDBConfig {
  @IsString() uri!: string;
  @IsString() dbName!: string;
  @IsNumber() poolSize!: number;
  @IsNumber() timeoutMS!: number;
  @IsNumber() socketTimeoutMS!: number;
  @IsNumber() retryAttempts!: number;
  @IsNumber() retryDelay!: number;
  @IsBoolean() autoIndex!: boolean;
}

class DatabaseConfig {
  @ValidateNested()
  @Type(() => MongoDBConfig)
  mongodb!: MongoDBConfig;
}

class EncryptionConfig {
  @IsString()
  secretKey!: string;
}

class GoogleConfig {
  @IsString() clientId!: string;
  @IsString() clientSecret!: string;
  @IsString() callbackUrl!: string;
}

class LinkedInConfig {
  @IsString()
  clientId!: string;

  @IsString()
  clientSecret!: string;

  @IsString()
  redirectUri!: string;
}

export class EnvironmentConfig {
  @ValidateNested()
  @Type(() => DatabaseConfig)
  database!: DatabaseConfig;

  @ValidateNested()
  @Type(() => EncryptionConfig)
  encryption!: EncryptionConfig;

  @ValidateNested()
  @Type(() => GoogleConfig)
  google!: GoogleConfig;

  @ValidateNested()
  @Type(() => LinkedInConfig)
  linkedin!: LinkedInConfig;
}

// === Helper Functions ===

function loadYamlFile<T>(filePath: string): T {
  const fileContent = readFileSync(filePath, 'utf8');
  return load(fileContent) as T;
}

// === Config Factory ===

export default (): Record<string, any> => {
  const env = process.env.NODE_ENV || 'local';
  const rawConfig = loadYamlFile<EnvironmentConfig>(getConfigPath(env));

  const validatedConfig = plainToInstance(EnvironmentConfig, rawConfig);
  const errors = validateSync(validatedConfig);
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed: ${errors.toString()}`);
  }
  // Convert class object to plain JS object
  return validatedConfig;
};
