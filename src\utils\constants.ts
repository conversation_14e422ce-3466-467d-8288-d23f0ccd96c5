export enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
  OTHER = 'Other',
}

export const ROLE = {
  ADMIN: 'Admin',
  CANDIDATE: 'Candidate',
  EMPLOYEE: 'Employee',
};

export const TOKEN_EXPIRATION = {
  ACCESS_TOKEN: '1h',
  REFRESH: '7d',
  RESET_PASSWORD: '15m',
  EMAIL_VERIFICATION: '24h',
};

export const httpStatusCodes = {
  200: 'Success',
  201: 'Created',
  400: 'Bad Request',
  401: 'Unauthorized',
  403: 'Forbidden',
  404: 'Not Found',
  500: 'Internal Server Error',
};

export const ENVIRONMENT = ['development', 'production'];

export const BLOCKED_EMAIL_DOMAINS = [
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
];
