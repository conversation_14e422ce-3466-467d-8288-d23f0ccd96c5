import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const token = request.cookies.get('authToken')?.value;
  const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

  // Protect routes defined in matcher
  if (!token) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  // Only check domain verification for authenticated users on protected routes
  if (token) {
    try {
      const res = await fetch(
        `${backendUrl}/employees/domain-verification-status`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (res.ok) {
        const data = await res.json();
        // Get role from cookie
        const roleName = request.cookies.get('roleName')?.value;
        // Example: { domainVerified: false }
        if (
          roleName?.toLowerCase() === 'employee' &&
          (data.data.domainVerified === null ||
            data.data.domainVerified === false)
        ) {
          return NextResponse.redirect(
            new URL('/request-domain-verification', request.url)
          );
        }
      } else {
        // If the token is invalid or the API fails, redirect to login
        return NextResponse.redirect(new URL('/auth/login', request.url));
      }
    } catch (error) {
      // On fetch error, redirect to login
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/employee/:path*'],
};
