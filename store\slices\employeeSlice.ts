import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type {
  RequestVerificationRequest,
  RequestVerificationResponse,
  VerifyDomainRequest,
  VerifyDomainResponse,
  CreateJobRequest,
  CreateJobResponse,
  GetEmployerJobsResponse,
  GetDomainVerificationStatusResponse,
} from '@/types/employee';
import Cookies from 'js-cookie';

const BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';

export interface EmployeeState {
  verificationMessage: string | null;
  verifyDomainMessage: string | null;
  createJobResult: CreateJobResponse | null;
  employerJobs: GetEmployerJobsResponse | null;
  domainVerificationStatus: GetDomainVerificationStatusResponse | null;
  loading: boolean;
  error: string | null;
}

const initialState: EmployeeState = {
  verificationMessage: null,
  verifyDomainMessage: null,
  createJobResult: null,
  employerJobs: null,
  domainVerificationStatus: null,
  loading: false,
  error: null,
};

export const requestVerification = createAsyncThunk<
  RequestVerificationResponse,
  RequestVerificationRequest
>('employee/requestVerification', async ({ email }, { rejectWithValue }) => {
  try {
    const token =
      typeof window !== 'undefined' ? Cookies.get('authToken') : null;
    const res = await fetch(`${BASE_URL}/employees/request-verification`, {
      method: 'POST',
      body: JSON.stringify({ email }),
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include',
    });
    const json = await res.json();
    if (!res.ok) throw new Error(json.message || 'Request failed');
    return json;
  } catch (err: any) {
    return rejectWithValue(err.message);
  }
});

export const verifyDomain = createAsyncThunk<
  VerifyDomainResponse,
  VerifyDomainRequest
>('employee/verifyDomain', async ({ token }, { rejectWithValue }) => {
  try {
    const authToken =
      typeof window !== 'undefined' ? Cookies.get('authToken') : null;
    const res = await fetch(`${BASE_URL}/employees/verify/${token}`, {
      credentials: 'include',
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    });
    const json = await res.json();
    if (!res.ok) throw new Error(json.message || 'Verification failed');
    return json;
  } catch (err: any) {
    return rejectWithValue(err.message);
  }
});

export const createJob = createAsyncThunk<CreateJobResponse, CreateJobRequest>(
  'employee/createJob',
  async (jobData, { rejectWithValue }) => {
    try {
      const token =
        typeof window !== 'undefined' ? Cookies.get('authToken') : null;
      const res = await fetch(`${BASE_URL}/employees`, {
        method: 'POST',
        body: JSON.stringify(jobData),
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        credentials: 'include',
      });
      const json = await res.json();
      if (!res.ok) throw new Error(json.message || 'Create job failed');
      return json;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

export const getEmployerJobs = createAsyncThunk<
  GetEmployerJobsResponse,
  string
>('employee/getEmployerJobs', async (employerId, { rejectWithValue }) => {
  try {
    const token =
      typeof window !== 'undefined' ? Cookies.get('authToken') : null;
    const res = await fetch(`${BASE_URL}/employees/employer/${employerId}`, {
      credentials: 'include',
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
    const json = await res.json();
    if (!res.ok) throw new Error(json.message || 'Get jobs failed');
    return json;
  } catch (err: any) {
    return rejectWithValue(err.message);
  }
});

export const getDomainVerificationStatus = createAsyncThunk<
  GetDomainVerificationStatusResponse,
  void
>('employee/getDomainVerificationStatus', async (_, { rejectWithValue }) => {
  try {
    const token =
      typeof window !== 'undefined' ? Cookies.get('authToken') : null;
    const res = await fetch(
      `${BASE_URL}/employees/domain-verification-status`,
      {
        credentials: 'include',
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      }
    );
    const json = await res.json();
    if (!res.ok) throw new Error(json.message || 'Get status failed');
    return json.data;
  } catch (err: any) {
    return rejectWithValue(err.message);
  }
});

const employeeSlice = createSlice({
  name: 'employee',
  initialState,
  reducers: {
    clearEmployeeState(state) {
      state.verificationMessage = null;
      state.verifyDomainMessage = null;
      state.createJobResult = null;
      state.employerJobs = null;
      state.domainVerificationStatus = null;
      state.error = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(requestVerification.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(requestVerification.fulfilled, (state, action) => {
        state.loading = false;
        state.verificationMessage = action.payload.message;
      })
      .addCase(requestVerification.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(verifyDomain.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyDomain.fulfilled, (state, action) => {
        state.loading = false;
        state.verifyDomainMessage = action.payload.message;
      })
      .addCase(verifyDomain.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(createJob.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createJob.fulfilled, (state, action) => {
        state.loading = false;
        state.createJobResult = action.payload;
      })
      .addCase(createJob.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getEmployerJobs.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getEmployerJobs.fulfilled, (state, action) => {
        state.loading = false;
        state.employerJobs = action.payload;
      })
      .addCase(getEmployerJobs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(getDomainVerificationStatus.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDomainVerificationStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.domainVerificationStatus = action.payload;
      })
      .addCase(getDomainVerificationStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearEmployeeState } = employeeSlice.actions;
export default employeeSlice.reducer;
