import {
  <PERSON>,
  Get,
  Param,
  Post,
  Body,
  UseGuards,
  Req,
} from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { MailService } from '../mailer/mailer.service';
import { ConfigService } from '@nestjs/config';
import { CreateJobDto } from './dto/create-job.dto';
import { ApiTags, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Throttle } from '@nestjs/throttler';
import { JwtUser } from '../auth/dto/auth.dto';

@ApiTags('Employee')
@Controller('employees')
export class EmployeeController {
  constructor(
    private readonly employeeService: EmployeeService,
    private readonly emailService: MailService,
    private readonly configService: ConfigService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Throttle({ default: { limit: 3, ttl: 300000 } }) // 3 requests per 5 minutes
  @ApiBody({ type: String })
  @Post('request-verification')
  async requestVerification(
    @Body('email') email: string,
    @Req() req: { user: JwtUser },
  ) {
    console.log('Request verification called with email:', email);
    return this.employeeService.requestVerification(
      email,
      req.user,
      this.emailService,
      this.configService,
    );
  }

  @Get('verify/:token')
  async verify(@Param('token') token: string) {
    return this.employeeService.verifyDomain(token);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  async createJob(
    @Req() req: { user: JwtUser },
    @Body() createJobDto: CreateJobDto,
  ) {
    return this.employeeService.createJobWithEmployer(req.user, createJobDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('employer/:id')
  async getEmployerJobs(@Param('id') employerId: string) {
    const jobs = await this.employeeService.getJobsByEmployer(employerId);
    return { message: 'Employer jobs fetched', data: jobs };
  }

  @UseGuards(JwtAuthGuard)
  @Get('domain-verification-status')
  async getDomainVerificationStatus(@Req() req: { user: JwtUser }) {
    return this.employeeService.getDomainVerificationStatus(req.user);
  }
}
