import * as fs from 'fs';
import * as path from 'path';
import { ENVIRONMENT } from './constants';
import { doubleCsrf } from 'csrf-csrf';
import { ConfigService } from '@nestjs/config';
import { BLOCKED_EMAIL_DOMAINS } from './constants';
import { Types } from 'mongoose';
import { BadRequestException } from '@nestjs/common';
import * as crypto from 'crypto';

export function getConfigPath(env = 'local'): string {
  const configPath = path.join(
    process.cwd(),
    'config',
    `configuration.${env}.yml`,
  );
  console.log('Resolved config path:', configPath);
  return configPath;
}

export function createDoubleCsrfOptions(configService: ConfigService) {
  return {
    getSecret: () => configService.get<string>('csrf.secretKey'),
    cookieName: 'x-csrf-token',
    cookieOptions: {
      httpOnly: false,
      secure: ENVIRONMENT.includes(process.env.NODE_ENV),
      sameSite: ENVIRONMENT.includes(process.env.NODE_ENV)
        ? ('lax' as const)
        : ('strict' as const),
      domain: process.env.DOMAIN_NAME,
    },
    getTokenFromRequest: (req) =>
      req.headers['x-csrf-token'] || req.cookies['x-csrf-token'],
    getSessionIdentifier: (req): string => {
      return req.session?.userId || req.cookies?.['session-id'] || 'anonymous';
    },
  };
}

export function resolveTemplatePath(filename: string): string {
  const possiblePaths = [
    path.join(__dirname, '..', 'modules', 'mailer', filename), // dev (src)
    path.join(__dirname, filename), // prod (dist)
    path.join(process.cwd(), 'src', 'modules', 'mailer', filename), // direct from root
  ];
  for (const filePath of possiblePaths) {
    if (fs.existsSync(filePath)) {
      return filePath;
    }
  }
  throw new Error(`Template file not found: ${filename}`);
}

const csrfOptions = createDoubleCsrfOptions(new ConfigService());
export const { generateCsrfToken, doubleCsrfProtection } =
  doubleCsrf(csrfOptions);

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function extractDomain(email: string): string {
  return email.split('@')[1].toLowerCase();
}

export function isBlockedDomain(domain: string): boolean {
  return BLOCKED_EMAIL_DOMAINS.includes(domain);
}

export async function enforceRateLimit(domainModel: any, userId: string) {
  const recentRequest = await domainModel.findOne({
    userId: new Types.ObjectId(userId),
    createdAt: { $gte: new Date(Date.now() - 5 * 60 * 1000) },
  });
  if (recentRequest) {
    throw new BadRequestException(
      'Please wait 5 minutes before requesting another verification email.',
    );
  }
}

export function generateToken(): string {
  return crypto.randomBytes(32).toString('hex');
}
