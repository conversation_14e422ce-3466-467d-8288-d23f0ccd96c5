export interface ResetPasswordPayload {
  sub: string;
  email: string;
  iat: number;
  exp: number;
}

export interface GoogleIdTokenPayload {
  iss: string;
  azp: string;
  aud: string;
  sub: string;
  email: string;
  email_verified: boolean;
  nbf?: number;
  name: string;
  picture: string;
  given_name: string;
  family_name: string;
  iat: number;
  exp: number;
  jti?: string;
}

export interface JwtUser {
  userId: string;
  username: string;
  role: { name: string };
}
