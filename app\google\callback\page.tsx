'use client';

import { Suspense, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { PageLoader, ComponentLoader } from '@/components/ui/loading-spinner';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { useAppSelector, useAppDispatch } from '@/store';
import { getProfile } from '@/store/slices/authSlice';
import { useRouter } from 'next/navigation';
import LandingHeader from '@/components/layout/LandingHeader';
import Image from 'next/image';
import Cookies from 'js-cookie';

const LazyFeatureGrid = dynamic(
  () => import('@/components/feature-grid').then(mod => mod.FeatureGrid),
  {
    loading: () => <ComponentLoader />,
    ssr: false,
  }
);

export default function Home() {
  const dispatch = useAppDispatch();
  const profileData = useAppSelector(state => state.auth.user);
  const isLoading = useAppSelector(state => state.auth.isLoading);
  const user = profileData;
  const router = useRouter();

  useEffect(() => {
    if (!profileData && !isLoading) {
      // Only call getProfile if we have an auth token
      const token =
        typeof window !== 'undefined' ? Cookies.get('authToken') : null;
      if (token) {
        dispatch(getProfile());
      }
    }
  }, [dispatch, profileData, isLoading]);

  useEffect(() => {
    if (user) {
      if (user.role?.name === 'employee') {
        router.replace('/dashboard');
      } else if (user.role?.name === 'candidate') {
        router.replace('/candidate-profile');
      }
    }
  }, [user, router]);

  if (isLoading) {
    return <PageLoader />;
  }
  if (user) {
    // Optionally, you can return null or a loader while redirecting
    return null;
  }

  // const dispatch = useAppDispatch();
  // const { value, loading } = useAppSelector(state => state.counter);

  return (
    <Suspense fallback={<PageLoader />}>
      <LandingHeader />
      <div
        className="min-h-screen pt-24"
        style={{
          background: 'linear-gradient(180deg, #bad0fd 0%, #FFFFFF 100%)',
        }}
      >
        <section className="container mx-auto px-4 pt-12 pb-20 flex flex-col items-center text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight">
            <span className="text-[#193E6C]">Where </span>
            <span className="text-[#3C9DF6]">Top Talents</span>
            <br />
            <span className="text-[#193E6C]">Meet Opportunities</span>
          </h1>
          <p className="text-xl max-w-2xl mx-auto mb-8 text-[#193E6C]">
            AI-powered talent matching platform that connects exceptional
            candidates with their dream jobs and helps employers find perfectly
            vetted talent.
          </p>
          <div className="flex gap-4 justify-center mb-8">
            <Link href="/choose-role">
              <Button className="bg-[#0858F8] text-white hover:bg-[#1e40af] border-2">
                Start Your Journey
              </Button>
            </Link>
            <Link href="#jobs">
              <Button
                variant="outline"
                className="border-[#0858F8] text-[#0858F8] bg-white hover:bg-[#e0e7ff]"
              >
                Browse Jobs
              </Button>
            </Link>
          </div>
        </section>
        {/* Dashboard Preview section */}
        <section className="flex justify-center items-center mb-20 relative">
          {/* Decorative background shape */}
          <div className="absolute -top-10 left-1/2 -translate-x-1/2 w-[90vw] h-48 bg-gradient-to-r from-[#fbe6d4] via-[#bad0fd] to-[#fbe6d4] opacity-40 rounded-3xl blur-2xl z-0" />
          {/* Dashboard mockup placeholder */}
          <div className="relative z-10 w-full max-w-4xl rounded-2xl shadow-2xl overflow-hidden border border-gray-200 bg-white">
            <div className="flex flex-col items-center justify-center h-[340px] md:h-[420px]">
              {/* Replace this with an actual dashboard image if available */}
              <span className="text-2xl text-gray-400">
                [ Dashboard Preview ]
              </span>
            </div>
          </div>
        </section>
        {/* Feature Highlights section */}
        <section className="container mx-auto px-4 mb-20">
          <div className="flex flex-col md:flex-row gap-8 justify-center items-stretch">
            {/* AI-Powered Matching */}
            <div className="flex-1 bg-white rounded-2xl shadow p-8 flex flex-col items-center text-center border border-gray-100">
              <div className="mb-4 text-[#3C9DF6]">
                {/* Lucide: BrainCog */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#193E6C]">
                AI-Powered Matching
              </h3>
              <p className="text-gray-500">
                All powered algorithms match candidates with the perfect job
                based on skills, culture fit, and performance.
              </p>
            </div>
            {/* Blockchain Verified */}
            <div className="flex-1 bg-white rounded-2xl shadow p-8 flex flex-col items-center text-center border border-gray-100">
              <div className="mb-4 text-[#3C9DF6]">
                {/* Lucide: ShieldCheck */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4l7 4v4c0 5-3.5 9-7 9s-7-4-7-9V8l7-4z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#193E6C]">
                Blockchain Verified
              </h3>
              <p className="text-gray-500">
                All candidate profiles are blockchain verified for authenticity
                and trust.
              </p>
            </div>
            {/* Smart Analytics */}
            <div className="flex-1 bg-white rounded-2xl shadow p-8 flex flex-col items-center text-center border border-gray-100">
              <div className="mb-4 text-[#3C9DF6]">
                {/* Lucide: BarChart2 */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 3v18h18"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M18 17V9M13 17V5M8 17v-3"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2 text-[#193E6C]">
                Smart Analytics
              </h3>
              <p className="text-gray-500">
                Track applications, interview stages, and get insights to
                improve your success rate.
              </p>
            </div>
          </div>
        </section>
        {/* Info Cards section */}
        <section className="container mx-auto px-4 mb-20 grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Card 1 */}
          <div className="rounded-2xl bg-gradient-to-br from-[#e6f0fd] to-[#f5f7fa] p-8 flex flex-col md:flex-row items-center gap-6 shadow">
            {/* Placeholder SVG */}
            <div className="flex-shrink-0">
              <svg width="90" height="90" viewBox="0 0 90 90" fill="none">
                <circle cx="45" cy="45" r="45" fill="#bad0fd" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg md:text-xl font-semibold text-[#193E6C] mb-2">
                We’re always here. Employees come and go.
              </h3>
              <p className="text-gray-600 mb-4">
                Discover the optimal match for your startup and get the best
                results possible.
              </p>
              <div className="flex gap-2">
                <Button className="bg-[#0858F8] text-white">
                  Start for free
                </Button>
                <Button
                  variant="outline"
                  className="border-[#0858F8] text-[#0858F8]"
                >
                  Book a demo
                </Button>
              </div>
            </div>
          </div>
          {/* Card 2 */}
          <div className="rounded-2xl bg-gradient-to-br from-[#fbe6d4] to-[#f7f0fa] p-8 flex flex-col md:flex-row items-center gap-6 shadow">
            <div className="flex-shrink-0">
              <svg width="90" height="90" viewBox="0 0 90 90" fill="none">
                <circle cx="45" cy="45" r="45" fill="#fbe6d4" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg md:text-xl font-semibold text-[#193E6C] mb-2">
                Leverage global world-class talented people.
              </h3>
              <p className="text-gray-600 mb-4">
                Hire/engage pre-vetted top 0.1% candidates from different
                industries and locations.
              </p>
              <div className="flex gap-2">
                <Button className="bg-[#0858F8] text-white">
                  Start for free
                </Button>
                <Button
                  variant="outline"
                  className="border-[#0858F8] text-[#0858F8]"
                >
                  Book a demo
                </Button>
              </div>
            </div>
          </div>
          {/* Card 3 */}
          <div className="rounded-2xl bg-gradient-to-br from-[#e6fdf0] to-[#f5faf7] p-8 flex flex-col md:flex-row items-center gap-6 shadow">
            <div className="flex-shrink-0">
              <svg width="90" height="90" viewBox="0 0 90 90" fill="none">
                <circle cx="45" cy="45" r="45" fill="#bafde0" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg md:text-xl font-semibold text-[#193E6C] mb-2">
                Find your perfect candidate on all industries.
              </h3>
              <p className="text-gray-600 mb-4">
                Access top talent across industries: designers, developers,
                product managers, and more.
              </p>
              <div className="flex gap-2">
                <Button className="bg-[#0858F8] text-white">
                  Start for free
                </Button>
                <Button
                  variant="outline"
                  className="border-[#0858F8] text-[#0858F8]"
                >
                  Book a demo
                </Button>
              </div>
            </div>
          </div>
          {/* Card 4 */}
          <div className="rounded-2xl bg-gradient-to-br from-[#fdf6e6] to-[#faf7f0] p-8 flex flex-col md:flex-row items-center gap-6 shadow">
            <div className="flex-shrink-0">
              <svg width="90" height="90" viewBox="0 0 90 90" fill="none">
                <circle cx="45" cy="45" r="45" fill="#fde6b9" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg md:text-xl font-semibold text-[#193E6C] mb-2">
                The all-in-one hiring platform
              </h3>
              <p className="text-gray-600 mb-4">
                Get more done and maximize the conversion rates. Discover the
                most productive channels.
              </p>
              <div className="flex gap-2">
                <Button className="bg-[#0858F8] text-white">
                  Start for free
                </Button>
                <Button
                  variant="outline"
                  className="border-[#0858F8] text-[#0858F8]"
                >
                  Book a demo
                </Button>
              </div>
            </div>
          </div>
        </section>
        {/* Partners/Tags section */}
        <section className="container mx-auto px-4 mb-20">
          <div className="mb-8 text-center font-semibold text-[#193E6C]">
            We partner with the best
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 mb-8">
            {/* Placeholder partner logos */}
            <div className="h-8 flex items-center">
              <span className="font-bold text-lg text-[#3C9DF6]">DevWise</span>
            </div>
            <div className="h-8 flex items-center">
              <span className="font-bold text-lg text-[#193E6C]">Invert</span>
            </div>
            <div className="h-8 flex items-center">
              <span className="font-bold text-lg text-[#3C9DF6]">Proline</span>
            </div>
            <div className="h-8 flex items-center">
              <span className="font-bold text-lg text-[#193E6C]">Flash</span>
            </div>
            <div className="h-8 flex items-center">
              <span className="font-bold text-lg text-[#3C9DF6]">Hiitech</span>
            </div>
          </div>
          <div className="flex flex-wrap justify-center gap-2">
            {/* Static tags/filters */}
            {[
              'PROJECT MANAGERS',
              'DEVELOPERS',
              'PRODUCT MANAGERS',
              'DESIGNERS',
              'COPYWRITERS',
              'ADMIN',
              'FINANCE',
              'MARKETING',
              'SALES',
              'HIRING',
              'DEVOPS',
              'LEGAL',
            ].map(tag => (
              <span
                key={tag}
                className="px-4 py-1 rounded-full bg-[#f3f6fa] text-[#193E6C] text-sm font-medium border border-[#e0e7ef]"
              >
                {tag}
              </span>
            ))}
          </div>
        </section>
        {/* Testimonial section */}
        <section className="w-full bg-gradient-to-r from-[#3C9DF6] to-[#193E6C] py-16 mb-20">
          <div className="container mx-auto px-4 flex flex-col items-center text-center text-white">
            <div className="mb-8">
              <Image
                src="https://randomuser.me/api/portraits/men/32.jpg"
                alt="User"
                width={96}
                height={96}
                className="w-24 h-24 rounded-full border-4 border-white shadow-lg mx-auto mb-4"
              />
              <div className="text-2xl font-semibold mb-2">
                “We came at the right time when started to scale our agency. The
                tool is saving us a lot of time and we are more efficient than
                ever. No operations easier than ever.”
              </div>
              <div className="text-lg font-medium mt-2">Miles Carlson</div>
              <div className="text-sm opacity-80">Project Manager, Invert</div>
            </div>
          </div>
        </section>
        {/* CTA section */}
        <section className="container mx-auto px-4 mb-20">
          <div className="flex flex-col md:flex-row items-center gap-12 bg-[#f5f8fd] rounded-2xl p-8 md:p-16 shadow">
            {/* Text content */}
            <div className="flex-1 text-center md:text-left">
              <h2 className="text-2xl md:text-3xl font-bold text-[#193E6C] mb-4">
                Ready to hire the talents you were looking for?
              </h2>
              <p className="text-gray-600 mb-6">
                Get more done and maximize the conversion rates. Discover the
                most productive channels.
              </p>
              <Link href="/choose-role">
                <Button className="bg-[#0858F8] text-white">
                  Start Your Journey
                </Button>
              </Link>
            </div>
            {/* Dashboard/Profile preview placeholder */}
            <div className="flex-1 flex justify-center">
              <div className="w-full max-w-md h-64 bg-white border border-gray-200 rounded-xl shadow flex items-center justify-center">
                <span className="text-gray-300 text-xl">
                  [ Profile Preview ]
                </span>
              </div>
            </div>
          </div>
        </section>
        {/* Features Grid - Lazy Loaded */}
        <LazyFeatureGrid />
      </div>
      {/* Footer section */}
      <footer className="w-full bg-[#f5f8fd] border-t border-[#e0e7ef] pt-12 pb-6 mt-8">
        <div className="container mx-auto px-4 flex flex-col md:flex-row justify-between items-start gap-8">
          {/* Logo and address */}
          <div className="flex-1 mb-8 md:mb-0">
            <div className="flex items-center gap-2 mb-4">
              <Image
                src="/assets/logo/TalentLoop.svg"
                alt="TalentLoop Logo"
                width={100}
                height={32}
                className="h-8 w-auto"
              />
            </div>
            <div className="text-gray-500 text-sm mb-2">
              234 Park Avenue, Manhattan, New York
            </div>
            <div className="text-gray-500 text-sm mb-2">
              <EMAIL>
            </div>
            <div className="text-gray-500 text-sm">+1 234 567 8900</div>
          </div>
          {/* Navigation links */}
          <div className="flex-1 flex flex-col md:flex-row gap-8 justify-end items-start">
            <div className="flex flex-col gap-2">
              <a href="#pricing" className="text-[#193E6C] hover:underline">
                Pricing
              </a>
              <a href="#contact" className="text-[#193E6C] hover:underline">
                Contact
              </a>
            </div>
            <div className="flex flex-col gap-2">
              <a href="/choose-role" className="text-[#193E6C] hover:underline">
                Sign Up
              </a>
              <a href="/auth/login" className="text-[#193E6C] hover:underline">
                Login
              </a>
            </div>
          </div>
        </div>
        <div className="container mx-auto px-4 mt-8 flex flex-col md:flex-row justify-between items-center text-xs text-gray-400 gap-2">
          <div>
            © {new Date().getFullYear()} TalentLoop. All rights reserved.
          </div>
          <div className="flex gap-4">
            <a href="#" className="hover:underline">
              Privacy Policy
            </a>
            <a href="#" className="hover:underline">
              Terms of Service
            </a>
            <a href="#" className="hover:underline">
              Cookie Policy
            </a>
          </div>
        </div>
      </footer>
    </Suspense>
  );
}
