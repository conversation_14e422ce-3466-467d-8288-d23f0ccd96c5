import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { CandidateProfile } from '@/types/candidateProfile';
import Cookies from 'js-cookie';

const BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000';
const AI_API_BASE_URL =
  process.env.NEXT_PUBLIC_AI_API_BASE_URL || 'http://localhost:5000';

export interface CandidateProfileState {
  data: CandidateProfile | null;
  completion: any;
  loading: boolean;
  error: string | null;
  resumeUploadResult: any;
  manualParseResult: any;
}

const initialState: CandidateProfileState = {
  data: null,
  completion: null,
  loading: false,
  error: null,
  resumeUploadResult: null,
  manualParseResult: null,
};

export const fetchCandidateProfile = createAsyncThunk(
  'candidateProfile/fetch',
  async (userId: string, { rejectWithValue }) => {
    try {
      const token =
        typeof window !== 'undefined' ? Cookies.get('authToken') : null;
      const res = await fetch(`${BASE_URL}/candidate-profile/user/${userId}`, {
        credentials: 'include',
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      });
      const json = await res.json();
      if (json.statusCode === 404) return null;
      return json.data;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

export const fetchCompletion = createAsyncThunk(
  'candidateProfile/fetchCompletion',
  async (userId: string, { rejectWithValue }) => {
    try {
      const token =
        typeof window !== 'undefined' ? Cookies.get('authToken') : null;
      const res = await fetch(
        `${BASE_URL}/candidate-profile/${userId}/completion`,
        {
          credentials: 'include',
          headers: token ? { Authorization: `Bearer ${token}` } : {},
        }
      );
      const json = await res.json();
      return json.data;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

export const uploadResume = createAsyncThunk(
  'candidateProfile/uploadResume',
  async (
    { file, userId }: { file: File; userId: string },
    { rejectWithValue }
  ) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const res = await fetch(`${AI_API_BASE_URL}/resume-parser/upload`, {
        method: 'POST',
        headers: { 'user-id': userId },
        body: formData,
      });
      const json = await res.json();
      return json;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

export const manualResumeParse = createAsyncThunk(
  'candidateProfile/manualResumeParse',
  async (
    { form, userId }: { form: Record<string, unknown>; userId: string },
    { rejectWithValue }
  ) => {
    try {
      const res = await fetch(`${AI_API_BASE_URL}/resume-parser/manual`, {
        method: 'POST',
        headers: {
          'user-id': userId,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ...form, userId }),
      });
      const json = await res.json();
      return json;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);

const candidateProfileSlice = createSlice({
  name: 'candidateProfile',
  initialState,
  reducers: {
    clearResumeUploadResult(state) {
      state.resumeUploadResult = null;
    },
    clearManualParseResult(state) {
      state.manualParseResult = null;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchCandidateProfile.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCandidateProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchCandidateProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchCompletion.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCompletion.fulfilled, (state, action) => {
        state.loading = false;
        state.completion = action.payload;
      })
      .addCase(fetchCompletion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(uploadResume.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(uploadResume.fulfilled, (state, action) => {
        state.loading = false;
        state.resumeUploadResult = action.payload;
      })
      .addCase(uploadResume.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(manualResumeParse.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(manualResumeParse.fulfilled, (state, action) => {
        state.loading = false;
        state.manualParseResult = action.payload;
      })
      .addCase(manualResumeParse.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearResumeUploadResult, clearManualParseResult } =
  candidateProfileSlice.actions;
export default candidateProfileSlice.reducer;
