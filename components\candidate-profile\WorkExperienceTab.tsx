import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Calendar as CalendarIcon } from 'lucide-react';
import type { WorkExperience } from '@/types/candidateProfile';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@/components/ui/popover';
import { format } from 'date-fns';

interface WorkExperienceTabProps {
  workExperience: WorkExperience[];
  editing: boolean;
  handleArrayAdd: (
    field: 'workExperience',
    emptyObj: Record<string, unknown>
  ) => void;
  handleArrayRemove: (field: 'workExperience', idx: number) => void;
  handleArrayChange: (
    field: 'workExperience',
    idx: number,
    subfield: string,
    value: string | number | boolean
  ) => void;
}

const WorkExperienceTab: React.FC<WorkExperienceTabProps> = ({
  workExperience,
  editing,
  handleArrayAdd,
  handleArrayRemove,
  handleArrayChange,
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <div className="font-semibold text-[#193E6C]">Work Experience</div>
        {editing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              handleArrayAdd('workExperience', {
                company: '',
                title: '',
                location: '',
                from_date: '',
                to_date: '',
                currentlyWorking: false,
                description: '',
              })
            }
          >
            <Plus className="w-4 h-4" /> Add
          </Button>
        )}
      </div>
      <div className="space-y-4">
        {(workExperience ?? []).map((work, idx) => (
          <div
            key={idx}
            className="bg-white rounded-lg p-4 flex flex-col gap-2 relative"
          >
            {editing && (
              <button
                className="absolute top-2 right-2 text-red-500"
                onClick={() => handleArrayRemove('workExperience', idx)}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
            <input
              className="input"
              placeholder="Company"
              value={work.company || ''}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'workExperience',
                  idx,
                  'company',
                  e.target.value
                )
              }
            />
            <input
              className="input"
              placeholder="Title"
              value={work.title || ''}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'workExperience',
                  idx,
                  'title',
                  e.target.value
                )
              }
            />
            <input
              className="input"
              placeholder="Location"
              value={work.location || ''}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'workExperience',
                  idx,
                  'location',
                  e.target.value
                )
              }
            />
            <div className="flex gap-2">
              {/* From Date Calendar */}
              <div className="flex flex-col">
                <span className="text-xs text-gray-500">From</span>
                {editing ? (
                  <Popover>
                    <PopoverTrigger asChild>
                      <button
                        type="button"
                        className="input flex items-center gap-2 cursor-pointer"
                      >
                        <span>
                          {work.from_date
                            ? format(new Date(work.from_date), 'yyyy-MM-dd')
                            : 'Select date'}
                        </span>
                        <CalendarIcon className="w-4 h-4 text-gray-400" />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={
                          work.from_date ? new Date(work.from_date) : undefined
                        }
                        onSelect={date =>
                          handleArrayChange(
                            'workExperience',
                            idx,
                            'from_date',
                            date ? date.toISOString() : ''
                          )
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                ) : (
                  <input
                    className="input"
                    placeholder="From"
                    value={
                      work.from_date
                        ? format(new Date(work.from_date), 'yyyy-MM-dd')
                        : ''
                    }
                    readOnly
                  />
                )}
              </div>
              {/* To Date Calendar */}
              <div className="flex flex-col">
                <span className="text-xs text-gray-500">To</span>
                {editing && !work.currentlyWorking ? (
                  <Popover>
                    <PopoverTrigger asChild>
                      <button
                        type="button"
                        className="input flex items-center gap-2 cursor-pointer"
                      >
                        <span>
                          {work.to_date
                            ? format(new Date(work.to_date), 'yyyy-MM-dd')
                            : 'Select date'}
                        </span>
                        <CalendarIcon className="w-4 h-4 text-gray-400" />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={
                          work.to_date ? new Date(work.to_date) : undefined
                        }
                        onSelect={date =>
                          handleArrayChange(
                            'workExperience',
                            idx,
                            'to_date',
                            date ? date.toISOString() : ''
                          )
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                ) : (
                  <input
                    className="input"
                    placeholder="To"
                    value={
                      work.to_date
                        ? format(new Date(work.to_date), 'yyyy-MM-dd')
                        : ''
                    }
                    readOnly
                    disabled={work.currentlyWorking}
                  />
                )}
              </div>
              <label className="flex items-center gap-1 text-xs">
                <input
                  type="checkbox"
                  checked={work.currentlyWorking}
                  disabled={!editing}
                  onChange={e =>
                    handleArrayChange(
                      'workExperience',
                      idx,
                      'currentlyWorking',
                      e.target.checked
                    )
                  }
                />
                Currently Working
              </label>
            </div>
            <textarea
              className="input"
              placeholder="Description"
              value={work.description || ''}
              readOnly={!editing}
              onChange={e =>
                handleArrayChange(
                  'workExperience',
                  idx,
                  'description',
                  e.target.value
                )
              }
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default WorkExperienceTab;
