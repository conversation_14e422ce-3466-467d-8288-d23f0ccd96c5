'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/store';
import { verifyDomain } from '@/store/slices/employeeSlice';
import { getProfile } from '@/store/slices/authSlice';
import Image from 'next/image';
import { toast } from 'react-hot-toast';
import Cookies from 'js-cookie';

export default function VerifyDomainPage() {
  const [checkStatus, setCheckStatus] = useState<
    'idle' | 'checking' | 'verified' | 'error'
  >('idle');
  const [checkError, setCheckError] = useState('');
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  const dispatch = useAppDispatch();
  const user = useAppSelector((state: any) => state.auth.user);
  const profileLoading = useAppSelector((state: any) => state.auth.isLoading);

  useEffect(() => {
    // Ensure user profile is loaded only if we have an auth token
    if (!user && !profileLoading) {
      const token =
        typeof window !== 'undefined' ? Cookies.get('authToken') : null;
      if (token) {
        dispatch(getProfile());
      }
    }
    // If no token, check domain status and redirect
    // Remove all domain verification logic
  }, [user, profileLoading, dispatch, router, token]);
  const handleVerify = async () => {
    if (!token) return;
    setCheckStatus('checking');
    setCheckError('');
    try {
      await dispatch(verifyDomain({ token: token ?? '' })).unwrap();
      setCheckStatus('verified');
      setCheckError('');
      setTimeout(() => router.replace('/auth/login'), 2000);
    } catch (err: any) {
      setCheckStatus('error');
      let errorMsg = 'Verification failed.';
      if (typeof err === 'string') {
        errorMsg = err;
      } else if (err && typeof err === 'object') {
        errorMsg = err?.message || err?.data?.message || errorMsg;
      }
      setCheckError(errorMsg);
      toast.error(errorMsg);
    }
  };

  if (!token) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen py-8 px-4">
        <h1 className="text-2xl font-bold mb-4">Domain Verification</h1>
        <p className="mb-6 text-center max-w-md">
          {profileLoading
            ? 'Checking your verification status...'
            : 'Redirecting...'}
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen py-8 px-4">
      <Image
        src="/assets/logo/TalentLoop.svg"
        alt="TalentLoop Logo"
        width={120}
        height={40}
        style={{ height: 40 }}
      />
      <div className="mb-6 text-center max-w-md">
        {checkStatus === 'idle' && (
          <div className="flex flex-col items-center gap-4">
            <svg
              className="text-blue-600 animate-pop"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" strokeOpacity="0.2" />
              <path d="M6 12l4 4 6-6" />
            </svg>
            <p className="text-blue-600 font-semibold">
              Click the button below to verify your domain.
            </p>
            <button
              className="bg-[#1976F6] text-white rounded-lg px-6 py-3 font-semibold text-lg hover:bg-blue-700 transition disabled:opacity-50"
              onClick={handleVerify}
            >
              Verify
            </button>
          </div>
        )}
        {checkStatus === 'checking' && (
          <div className="flex flex-col items-center gap-2">
            <svg
              className="animate-spin-slow text-blue-600"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" strokeOpacity="0.2" />
              <path d="M6 12l4 4 6-6" />
            </svg>
            <p className="text-blue-600 font-semibold">
              Verifying your domain...
            </p>
          </div>
        )}
        {checkStatus === 'verified' && (
          <div className="flex flex-col items-center gap-2">
            <svg
              className="text-green-600 animate-pop"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" strokeOpacity="0.2" />
              <path d="M6 12l4 4 6-6" />
            </svg>
            <p className="text-green-600 font-semibold">
              Your domain is verified! You can now access the platform.
              Redirecting to login...
            </p>
          </div>
        )}
        {checkStatus === 'error' && (
          <div className="flex flex-col items-center gap-2">
            <svg
              className="text-red-600 animate-pop"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10" strokeOpacity="0.2" />
              <line x1="15" y1="9" x2="9" y2="15" />
              <line x1="9" y1="9" x2="15" y2="15" />
            </svg>
            <p className="text-red-600 font-semibold">{checkError}</p>
          </div>
        )}
      </div>
    </div>
  );
}
