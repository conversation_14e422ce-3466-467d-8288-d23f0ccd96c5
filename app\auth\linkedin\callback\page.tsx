'use client';
import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAppDispatch } from '@/store';
import { linkedinSignup, linkedinSignin } from '@/store/slices/authSlice';
import toast from 'react-hot-toast';
import Cookies from 'js-cookie';

export default function LinkedInCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  const error = searchParams.get('error');
  const dispatch = useAppDispatch();

  // Clear any LinkedIn-related data and restart auth flow
  const restartLinkedInAuth = (isSignup = false) => {
    // Clear any stored LinkedIn data
    localStorage.removeItem('linkedin_state');
    localStorage.removeItem('linkedin_code');

    if (isSignup) {
      // For signup, clear role selection to force fresh selection
      localStorage.removeItem('selectedRoleId');
      router.replace('/choose-role');
    } else {
      // For signin, go back to login
      router.replace('/auth/login');
    }
  };

  useEffect(() => {
    if (error) {
      toast.error(error || 'LinkedIn login failed. Please try again.');
      return;
    }
    if (!code) {
      toast.error('No code found in URL. Please try logging in again.');
      return;
    }

    // Check if this code has already been used
    const lastUsedCode = localStorage.getItem('linkedin_last_code');
    if (lastUsedCode === code) {
      toast.error('This LinkedIn authorization has already been used. Please try again.');
      setTimeout(() => {
        restartLinkedInAuth(false);
      }, 2000);
      return;
    }

    // Store the code to prevent reuse
    localStorage.setItem('linkedin_last_code', code);
    // Determine if this is signup or signin based on localStorage or route
    const isSignup = localStorage.getItem('selectedRoleId');
    if (isSignup) {
      const role = localStorage.getItem('selectedRoleId') || '';
      dispatch(linkedinSignup({ code, role }) as any)
        .unwrap()
        .then((result: any) => {
          if (result?.data?.access_token) {
            Cookies.set('authToken', result.data.access_token, {
              expires: 7,
              path: '/',
            });
          }
          // Clear the used code
          localStorage.removeItem('linkedin_last_code');
          toast.success('Sign in with LinkedIn successful! Redirecting...');
          setTimeout(() => {
            router.replace('/dashboard');
          }, 1500);
        })
        .catch((err: any) => {
          const errorMessage = err?.message || 'LinkedIn signup failed';

          // Handle specific LinkedIn errors
          if (errorMessage.includes('revoked') || errorMessage.includes('REVOKED_ACCESS_TOKEN')) {
            toast.error('LinkedIn access was revoked. Please try signing up again.');
            setTimeout(() => {
              restartLinkedInAuth(true);
            }, 2000);
          } else if (errorMessage.includes('expired')) {
            toast.error('LinkedIn authorization expired. Please try signing up again.');
            setTimeout(() => {
              restartLinkedInAuth(true);
            }, 2000);
          } else {
            toast.error(errorMessage);
          }
        });
    } else {
      dispatch(linkedinSignin({ code }) as any)
        .unwrap()
        .then((result: any) => {
          if (result?.data?.access_token) {
            Cookies.set('authToken', result.data.access_token, {
              expires: 7,
              path: '/',
            });
          }
          // Clear the used code
          localStorage.removeItem('linkedin_last_code');
          toast.success('Sign in with LinkedIn successful! Redirecting...');
          setTimeout(() => {
            router.replace('/dashboard');
          }, 1500);
        })
        .catch((err: any) => {
          const errorMessage = err?.message || 'LinkedIn login failed';

          // Handle specific LinkedIn errors
          if (errorMessage.includes('revoked') || errorMessage.includes('REVOKED_ACCESS_TOKEN')) {
            toast.error('LinkedIn access was revoked. Please try signing in again.');
            setTimeout(() => {
              restartLinkedInAuth(false);
            }, 2000);
          } else if (errorMessage.includes('expired')) {
            toast.error('LinkedIn authorization expired. Please try signing in again.');
            setTimeout(() => {
              restartLinkedInAuth(false);
            }, 2000);
          } else if (errorMessage.includes('User not found')) {
            toast.error('User not found');
            setTimeout(() => {
              // Clear any LinkedIn-related data
              localStorage.removeItem('linkedin_state');
              localStorage.removeItem('linkedin_code');
              localStorage.removeItem('linkedin_last_code');
              // Redirect back to login page
              router.replace('/auth/login');
            }, 2000);
          } else {
            toast.error(errorMessage);
          }
        });
    }
  }, [code, error, dispatch, router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <span>Processing LinkedIn login...</span>
    </div>
  );
}
