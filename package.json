{"name": "talentloop", "version": "0.1.0", "private": true, "packageManager": "pnpm@8.15.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "clean": "rm -rf .next out", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@next/swc-wasm-nodejs": "13.5.6", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.0.1", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.6.1", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.3", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.446.0", "mongoose": "^8.16.1", "next": "^14.0.4", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "postcss": "^8.4.32", "qs": "^6.14.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-easy-crop": "^5.4.2", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-linkedin-login-oauth2": "^2.0.1", "react-redux": "^9.0.4", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@types/bcryptjs": "^3.0.0", "@types/eslint": "^8.56.2", "@types/passport-google-oauth20": "^2.0.16", "@types/qs": "^6.14.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "cpx": "^1.5.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1"}}